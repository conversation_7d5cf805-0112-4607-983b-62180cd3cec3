#!/bin/bash

#SBATCH -p i64m1tga40u       
#SBATCH --gres=gpu:1            
#SBATCH -n 4                    
#SBATCH -J rram_write_test      
#SBATCH -o write_1yi_%j.log     
#SBATCH -e error_%j.log         

echo "作业启动时间: $(date)"
echo "当前工作目录: $(pwd)"
echo "作业将被调度到节点: $SLURM_JOB_NODELIST"

source .venv/bin/activate
echo "Python virtual environment activated."

python accuracy_test/rram_write_test.py
echo "Python script finished."

echo "作业结束时间: $(date)"